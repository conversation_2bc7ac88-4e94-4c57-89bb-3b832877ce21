<template>
  <div class="environmental-overview-page">
    <div id="screen-map" class="screen-map"></div>
    <div class="main-background"></div>

    <!-- 左侧树形导航 -->
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          v-if="mainModel === '0'"
          ref="monitorListRef"
          :tree-data="treeData"
          :field-names="defaultProps"
          :filter-tree-node="filterNode"
          :expand-on-click-node="false"
          @select="handleNodeClick"
        />
        <a-tree
          v-if="mainModel === '1'"
          ref="monitorListRef"
          :tree-data="treeDataJy"
          :field-names="defaultProps"
          :filter-tree-node="filterNode"
          :expand-on-click-node="false"
          @select="handleNodeClick"
        >
          <template #title="{ data }">
            <div class="custom-tree-node">
              <span>{{ data.label }}</span>
            </div>
          </template>
        </a-tree>
      </div>
    </div>

    <!-- 收缩按钮 -->
    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />

    <!-- 菜单图标 -->
    <div
      ref="menuRef"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>

    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <MenuDialog />
    </div>

    <!-- 地图控制按钮 -->
    <div :class="['control', isCollapsed ? 'control-s' : '']">
      <div class="control-div">
        <img
          class="img"
          src="@/assets/images/control1.png"
          alt=""
          @click="refreshMap"
        />
        <img class="img img1" src="@/assets/images/control3.png" alt="" />
        <div class="imgClick">
          <div @click="mapScale(0.5)"></div>
          <div @click="mapScale(-0.5)"></div>
        </div>
      </div>
    </div>

    <!-- 模式选择 -->
    <div :class="['select', isCollapsed && !isMenu ? 'select-s' : '']">
      <a-select
        v-model:value="mainModel"
        placeholder="请选择"
        @change="mainChange"
      >
        <a-select-option
          v-for="item in options"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>

    <!-- 污染源在线管理模块 -->
    <div v-if="mainModel === '0'" class="card">
      <div class="card-top">
        <CardCollapse
          :title="'数据统计'"
          :height="'400px'"
          :width="'800px'"
          @child="childHandle"
        >
          <template #content>
            <div class="select-jy select-jy-jy" style="width: 800px">
              <a-form layout="inline" :model="environmentalData">
                <a-form-item label="日期">
                  <a-date-picker
                    v-model:value="environmentalData.day"
                    placeholder="选择日期"
                  />
                </a-form-item>
                <a-form-item>
                  <a-button type="primary" @click="queryHandle">查询</a-button>
                </a-form-item>
              </a-form>
            </div>
            <div class="content-jy">
              <ColumnChart
                v-if="chartShow"
                :chart-data="columnDate"
                :time-data="[]"
                title="数据统计"
                :height="300"
              />
            </div>
          </template>
        </CardCollapse>
      </div>
    </div>

    <!-- 实时监测数据表格 -->
    <div
      v-if="!isCollapsed1 && mainModel === '0'"
      class="card"
      style="margin-top: 80px"
    >
      <!-- 废水实时监测 -->
      <a-card class="box-card">
        <template #title>
          <span>废水实时监测</span>
          <a-select
            v-show="show1"
            v-model:value="siteId"
            placeholder="请选择"
            style="margin-bottom: 8px"
            @change="chooseHandle"
          >
            <a-select-option
              v-for="item in sites"
              :key="item.id"
              :value="item.id"
            >
              {{ item.siteName }}
            </a-select-option>
          </a-select>
          <a-button
            type="link"
            style="
              position: absolute;
              z-index: 1999;
              display: flex;
              justify-content: flex-end;
              margin-left: 1rem;
              pointer-events: fill;
            "
            @click="openHandle(0)"
          >
            <span v-if="!show1">展开</span>
            <span v-if="show1">收缩</span>
          </a-button>
        </template>
        <div v-if="!show1" style="width: 120px"></div>
        <a-table
          v-show="show1"
          ref="table1Ref"
          :data-source="tableData1"
          :pagination="false"
          :scroll="{ y: height }"
          :row-class-name="tableRowClassName"
          @mouseenter="
            () => rollingTable('table1', rollingModel.rollingTableStop)
          "
          @mouseleave="
            () => rollingTable('table1', rollingModel.rollingTableStart)
          "
        >
          <a-table-column
            title="单位"
            data-index="site"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测点"
            data-index="monitorName"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测时间"
            data-index="mTime"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="COD"
            data-index="cwater011"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="氨氮"
            data-index="cwater060"
            align="center"
            :ellipsis="true"
          />
        </a-table>
      </a-card>

      <!-- 废气实时监测 -->
      <a-card class="box-card">
        <template #title>
          <span>废气实时监测</span>
          <a-button
            type="link"
            style="
              position: absolute;
              z-index: 1999;
              display: flex;
              justify-content: flex-end;
              margin-left: 1rem;
              pointer-events: fill;
            "
            @click="openHandle(1)"
          >
            <span v-if="!show2">展开</span>
            <span v-if="show2">收缩</span>
          </a-button>
        </template>
        <a-table
          v-show="show2"
          ref="table2Ref"
          :data-source="tableData2"
          :pagination="false"
          :scroll="{ y: height }"
          :row-class-name="tableRowClassName"
          @mouseenter="
            () => rollingTable('table2', rollingModel.rollingTableStop)
          "
          @mouseleave="
            () => rollingTable('table2', rollingModel.rollingTableStart)
          "
        >
          <a-table-column
            title="单位"
            data-index="site"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测点"
            data-index="monitorName"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测时间"
            data-index="mTime"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="SO₂实测"
            data-index="cair02R"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="NOX实测"
            data-index="cair03R"
            align="center"
            :ellipsis="true"
          />
        </a-table>
      </a-card>

      <!-- 超标情况监测 -->
      <a-card class="box-card">
        <template #title>
          <span>超标情况监测</span>
          <a-button
            type="link"
            style="
              position: absolute;
              z-index: 1999;
              display: flex;
              justify-content: flex-end;
              margin-left: 1rem;
              pointer-events: fill;
            "
            @click="openHandle(2)"
          >
            <span v-if="!show3">展开</span>
            <span v-if="show3">收缩</span>
          </a-button>
        </template>
        <a-tabs
          v-show="show3"
          v-model:active-key="activeName"
          style="
            position: absolute;
            z-index: 1999;
            display: flex;
            justify-content: flex-end;
            margin-bottom: 16px;
            pointer-events: fill;
          "
          @change="handleClick"
        >
          <a-tab-pane key="0" tab="废水超标情况" />
          <a-tab-pane key="1" tab="废气超标情况" />
        </a-tabs>

        <!-- 废水超标表格 -->
        <a-table
          v-show="show3"
          v-if="activeName === '0'"
          ref="table3Ref"
          :data-source="tableData3"
          :pagination="false"
          :scroll="{ y: height }"
          :row-class-name="tableRowClassName"
          style="margin-top: 40px"
          @mouseenter="
            () => rollingTable('table3', rollingModel.rollingTableStop)
          "
          @mouseleave="
            () => rollingTable('table3', rollingModel.rollingTableStart)
          "
        >
          <a-table-column
            title="单位"
            data-index="site"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测点"
            data-index="monitorName"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测时间"
            data-index="mTime"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="COD"
            data-index="cwater011"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="氨氮"
            data-index="cwater060"
            align="center"
            :ellipsis="true"
          />
        </a-table>

        <!-- 废气超标表格 -->
        <a-table
          v-show="show3"
          v-if="activeName === '1'"
          ref="table4Ref"
          :data-source="tableData4"
          :pagination="false"
          :scroll="{ y: height }"
          :row-class-name="tableRowClassName"
          style="margin-top: 40px"
          @mouseenter="
            () => rollingTable('table4', rollingModel.rollingTableStop)
          "
          @mouseleave="
            () => rollingTable('table4', rollingModel.rollingTableStart)
          "
        >
          <a-table-column
            title="单位"
            data-index="site"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测点"
            data-index="monitorName"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="监测时间"
            data-index="mTime"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="SO₂实测"
            data-index="cair02R"
            align="center"
            :ellipsis="true"
          />
          <a-table-column
            title="NOX实测"
            data-index="cair03R"
            align="center"
            :ellipsis="true"
          />
        </a-table>
      </a-card>
    </div>

    <!-- 甲烷管控模块 -->
    <div v-if="mainModel === '1'" class="card card-jy">
      <div class="card-top">
        <CardCollapse title="甲烷管控模块" @child="childHandle">
          <template v-if="chartShow" #content>
            <div class="select-jy select-jy-jy">
              <a-form layout="inline" :model="alarmSituationData">
                <a-form-item label="点位列表">
                  <a-select v-model:value="typeModeJy" placeholder="请选择">
                    <a-select-option
                      v-for="item in methaneOptions"
                      :key="item.id"
                      :value="item.id"
                    >
                      {{ item.deviceMark }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="开始时间">
                  <a-range-picker
                    v-model:value="timeArr"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
                <a-form-item>
                  <a-button type="primary" @click="onSubmit('0')">
                    实时查询
                  </a-button>
                  <a-button type="primary" @click="onSubmit('1')">
                    历史查询
                  </a-button>
                </a-form-item>
              </a-form>
            </div>
            <a-row>
              <a-col :span="12">
                <LineChart
                  :chart-data="data1.value"
                  :time-data="data1.time"
                  :height="200"
                  :legend="`浓度检测值 实时值${value1}ppm.m`"
                  :title="'浓度检测值'"
                  :point-value="0"
                  type="浓度检测值"
                  site-id=""
                  monitor-type=""
                />
              </a-col>
              <a-col :span="12">
                <LineChart
                  :chart-data="data2.value"
                  :time-data="data2.time"
                  :height="200"
                  :legend="`浓度检测报警 实时值${value2} ${value3}`"
                  :title="'浓度检测报警'"
                  :point-value="0"
                  type="浓度检测报警"
                  site-id=""
                  monitor-type=""
                />
              </a-col>
            </a-row>
          </template>
        </CardCollapse>
      </div>

      <!-- 气体泄漏报警统计 -->
      <div class="card-top">
        <CardCollapse title="气体泄漏报警统计">
          <template #content="{ isCollapsed }">
            <div v-if="isCollapsed">
              <div class="select-jy select-jy-jy">
                <a-form layout="inline" :model="alarmSituationData">
                  <a-form-item label="点位列表">
                    <a-select v-model:value="typeModeJyQt" placeholder="请选择">
                      <a-select-option
                        v-for="item in methaneOptions"
                        :key="item.id"
                        :value="item.id"
                      >
                        {{ item.deviceMark }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="开始时间">
                    <a-range-picker
                      v-model:value="timeArrQt"
                      show-time
                      format="YYYY-MM-DD HH:mm:ss"
                      :placeholder="['开始日期', '结束日期']"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" @click="onSubmitQt">查询</a-button>
                    <a-button type="primary" @click="resetHandle"
                      >重置</a-button
                    >
                  </a-form-item>
                </a-form>
              </div>
              <div style="width: 922px">
                <a-table
                  :data-source="alarmTableData"
                  :pagination="false"
                  :scroll="{ y: 300 }"
                  :row-class-name="tableRowClassName"
                >
                  <a-table-column
                    title="点位名称"
                    data-index="pointName"
                    align="center"
                    :ellipsis="true"
                  />
                  <a-table-column
                    title="报警时间"
                    data-index="alarmsDateStr"
                    align="center"
                    :ellipsis="true"
                  />
                  <a-table-column
                    title="备注"
                    data-index="remark"
                    align="center"
                    :ellipsis="true"
                  />
                </a-table>
                <a-pagination
                  v-model:current="alarmCurrentPage"
                  v-model:page-size="alarmPageSize"
                  :total="alarmTotal"
                  :page-size-options="['20', '40', '60', '80', '100']"
                  show-size-changer
                  show-quick-jumper
                  :show-total="(total: number) => `共 ${total} 条`"
                  style="margin-top: 20px; text-align: right"
                  @change="handleCurrentChange"
                  @show-size-change="handleSizeChange"
                />
              </div>
            </div>
          </template>
        </CardCollapse>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <DetailsDialog
      ref="detailsDialogRef"
      :device-list="deviceList"
      :site-short-name="siteShortName"
    />

    <!-- 预览图片 -->
    <transition name="fade">
      <img
        v-if="dialogVisible"
        :src="dialogImageUrl"
        alt="Preview"
        class="preview-image"
      />
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

// 导入组件
import MenuDialog from '@/components/menu.vue';
import CardCollapse from '@/components/card-collapse.vue';
import ColumnChart from './components/ColumnChart.vue';
import LineChart from './components/LineChart.vue';
import DetailsDialog from './components/details-dialog.vue';

// 导入API
import {
  getTreeNode,
  initAlarmStatistics,
  initMonitWastewaterList,
  initMonitWastegasList,
  initEnvinfoWarnList,
  getJoinDept,
  getDeptPoint,
  getRealTimeData,
  getHistoryData,
} from '@/http/environmental-overview';

// 类型定义
interface TreeNode {
  id: string;
  label: string;
  children?: TreeNode[];
  [key: string]: any;
}

interface EnvironmentalData {
  day: Dayjs | null;
}

interface AlarmSituationData {
  [key: string]: any;
}

interface TableData {
  site: string;
  monitorName: string;
  mTime: string;
  cwater011?: string;
  cwater060?: string;
  cair02R?: string;
  cair03R?: string;
  [key: string]: any;
}

interface ChartData {
  value: number[];
  time: string[];
}

interface RollingModel {
  rollingTableStart: string;
  rollingTableStop: string;
}

// 响应式数据

// 基础状态
const fullscreenLoading = ref(false);
const isCollapsed = ref(false);
const isCollapsed1 = ref(false);
const filterText = ref('');
const mainModel = ref('0');
const chartShow = ref(true);

// 树形数据
const treeData = ref<TreeNode[]>([]);
const treeDataJy = ref<TreeNode[]>([]);
const defaultProps = {
  children: 'children',
  title: 'label',
  key: 'id',
};

// 菜单和拖拽相关
const menuRef = ref<HTMLElement>();
const x = ref(100);
const y = ref(100);
const isDragging = ref(false);
const isMenu = ref(false);

// 表单数据
const environmentalData = reactive<EnvironmentalData>({
  day: dayjs(),
});

const alarmSituationData = reactive<AlarmSituationData>({});

// 图表数据
const columnDate = ref<number[][]>([]);
const data1 = ref<ChartData>({ value: [], time: [] });
const data2 = ref<ChartData>({ value: [], time: [] });
const value1 = ref(0);
const value2 = ref(0);
const value3 = ref('');

// 表格相关
const show1 = ref(true);
const show2 = ref(true);
const show3 = ref(true);
const height = ref(300);
const activeName = ref('0');

// 站点和选择相关
const siteId = ref('');
const sites = ref<any[]>([]);
const typeModeJy = ref('');
const typeModeJyQt = ref('');
const methaneOptions = ref<any[]>([]);

// 时间选择
const timeArr = ref<[Dayjs, Dayjs] | undefined>(undefined);
const timeArrQt = ref<[Dayjs, Dayjs] | undefined>(undefined);

// 表格数据
const tableData1 = ref<TableData[]>([]);
const tableData2 = ref<TableData[]>([]);
const tableData3 = ref<TableData[]>([]);
const tableData4 = ref<TableData[]>([]);
const alarmTableData = ref<any[]>([]);

// 分页数据
const alarmCurrentPage = ref(1);
const alarmPageSize = ref(20);
const alarmTotal = ref(0);

// 设备和对话框相关
const deviceList = ref<any[]>([]);
const siteShortName = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');

// 引用
const monitorListRef = ref();
const detailsDialogRef = ref();
const table1Ref = ref();
const table2Ref = ref();
const table3Ref = ref();
const table4Ref = ref();

// 选项数据
const options = ref([
  { label: '污染源在线管理', value: '0' },
  { label: '甲烷管控模块', value: '1' },
]);

// 滚动模型
const rollingModel: RollingModel = {
  rollingTableStart: 'start',
  rollingTableStop: 'stop',
};

// 方法定义
const filterTreeData = (data: TreeNode[], keyword: string): TreeNode[] => {
  return data.filter(item => {
    if (item.label.includes(keyword)) return true;
    if (item.children) {
      item.children = filterTreeData(item.children, keyword);
      return item.children.length > 0;
    }
    return false;
  });
};

const filterNode = (value: string, data: TreeNode) => {
  if (!value) return true;
  return data.label.includes(value);
};

const handleNodeClick = (selectedKeys: any[], info: any) => {
  console.log('节点点击:', selectedKeys, info);
  if (selectedKeys.length > 0) {
    const nodeData = info.node;
    // 处理节点点击逻辑
    loadNodeData(nodeData);
  }
};

const loadNodeData = async (nodeData: any) => {
  try {
    fullscreenLoading.value = true;
    // 根据节点类型加载不同数据
    if (mainModel.value === '0') {
      await loadPollutionSourceData(nodeData);
    } else {
      await loadMethaneData(nodeData);
    }
  } catch (error) {
    console.error('加载节点数据失败:', error);
    message.error('加载数据失败');
  } finally {
    fullscreenLoading.value = false;
  }
};

const loadPollutionSourceData = async (nodeData: any) => {
  // 加载污染源数据
  const [wasteWaterRes, wasteGasRes, alarmRes] = await Promise.all([
    initMonitWastewaterList({ nodeId: nodeData.id }),
    initMonitWastegasList({ nodeId: nodeData.id }),
    initEnvinfoWarnList({ nodeId: nodeData.id }),
  ]);

  if ((wasteWaterRes as any).code === 200) {
    tableData1.value = (wasteWaterRes as any).data || [];
  }

  if ((wasteGasRes as any).code === 200) {
    tableData2.value = (wasteGasRes as any).data || [];
  }

  if ((alarmRes as any).code === 200) {
    tableData3.value = (alarmRes as any).data?.wasteWater || [];
    tableData4.value = (alarmRes as any).data?.wasteGas || [];
  }
};

const loadMethaneData = async (nodeData: any) => {
  // 加载甲烷数据
  const pointRes = await getDeptPoint({ deptId: nodeData.id });
  if ((pointRes as any).code === 200) {
    methaneOptions.value = (pointRes as any).data || [];
    if (methaneOptions.value.length > 0) {
      typeModeJy.value = methaneOptions.value[0].id;
      typeModeJyQt.value = methaneOptions.value[0].id;
      await loadRealTimeData();
    }
  }
};

const loadRealTimeData = async () => {
  if (!typeModeJy.value) return;

  try {
    const res = await getRealTimeData({ pointId: typeModeJy.value });
    if ((res as any).code === 200) {
      const data = (res as any).data;
      data1.value = {
        value: data.concentrationValues || [],
        time: data.timeLabels || [],
      };
      data2.value = {
        value: data.alarmValues || [],
        time: data.timeLabels || [],
      };
      value1.value = data.currentConcentration || 0;
      value2.value = data.currentAlarm || 0;
      value3.value = data.alarmUnit || '';
    }
  } catch (error) {
    console.error('加载实时数据失败:', error);
  }
};

// 拖拽相关方法
const startDrag = (event: MouseEvent) => {
  isDragging.value = true;
  const startX = event.clientX - x.value;
  const startY = event.clientY - y.value;

  const handleMouseMove = (e: MouseEvent) => {
    x.value = e.clientX - startX;
    y.value = e.clientY - startY;
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const iconHandle = () => {
  isMenu.value = !isMenu.value;
};

// 地图相关方法
const refreshMap = () => {
  // 刷新地图逻辑
  console.log('刷新地图');
};

const mapScale = (scale: number) => {
  // 地图缩放逻辑
  console.log('地图缩放:', scale);
};

// 模式切换
const mainChange = (value: any) => {
  mainModel.value = String(value);
  // 重新加载数据
  loadInitialData();
};

// 子组件处理
const childHandle = (data: any) => {
  console.log('子组件数据:', data);
  isCollapsed1.value = data.isCollapsed || false;
};

// 查询处理
const queryHandle = async () => {
  if (!environmentalData.day) {
    message.warning('请选择日期');
    return;
  }

  try {
    fullscreenLoading.value = true;
    const dateStr = environmentalData.day.format('YYYY-MM-DD');
    // 查询统计数据的逻辑
    await loadStatisticsData(dateStr);
  } catch (error) {
    console.error('查询失败:', error);
    message.error('查询失败');
  } finally {
    fullscreenLoading.value = false;
  }
};

const loadStatisticsData = async (date: string) => {
  // 加载统计数据
  const res = await initAlarmStatistics({ date });
  if ((res as any).code === 200) {
    columnDate.value = (res as any).data || [];
  }
};

// 选择处理
const chooseHandle = (value: any) => {
  siteId.value = String(value);
  // 根据选择的站点加载数据
  loadSiteData(String(value));
};

const loadSiteData = async (siteId: string) => {
  // 加载站点数据的逻辑
  console.log('加载站点数据:', siteId);
};

// 展开处理
const openHandle = (type: number) => {
  switch (type) {
    case 0:
      show1.value = !show1.value;
      break;
    case 1:
      show2.value = !show2.value;
      break;
    case 2:
      show3.value = !show3.value;
      break;
  }
};

// 表格滚动
const rollingTable = (tableId: string, action: string) => {
  console.log('表格滚动:', tableId, action);
  // 实现表格滚动逻辑
};

// 表格行样式
const tableRowClassName = ({ rowIndex }: { row: any; rowIndex: number }) => {
  if (rowIndex % 2 === 1) {
    return 'table-striped';
  }
  return '';
};

// 标签页切换
const handleClick = (key: any) => {
  activeName.value = String(key);
};

// 提交处理
const onSubmit = async (type: string) => {
  if (!typeModeJy.value) {
    message.warning('请选择点位');
    return;
  }

  try {
    fullscreenLoading.value = true;
    if (type === '0') {
      // 实时查询
      await loadRealTimeData();
    } else {
      // 历史查询
      await loadHistoryData();
    }
  } catch (error) {
    console.error('查询失败:', error);
    message.error('查询失败');
  } finally {
    fullscreenLoading.value = false;
  }
};

const loadHistoryData = async () => {
  if (!timeArr.value || !typeModeJy.value) return;

  const [startTime, endTime] = timeArr.value;
  const res = await getHistoryData({
    pointId: typeModeJy.value,
    startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
    endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
  });

  if ((res as any).code === 200) {
    const data = (res as any).data;
    data1.value = {
      value: data.concentrationValues || [],
      time: data.timeLabels || [],
    };
    data2.value = {
      value: data.alarmValues || [],
      time: data.timeLabels || [],
    };
  }
};

// 气体泄漏查询提交
const onSubmitQt = async () => {
  if (!typeModeJyQt.value) {
    message.warning('请选择点位');
    return;
  }

  try {
    fullscreenLoading.value = true;
    // 查询气体泄漏报警数据
    await loadAlarmData();
  } catch (error) {
    console.error('查询失败:', error);
    message.error('查询失败');
  } finally {
    fullscreenLoading.value = false;
  }
};

const loadAlarmData = async () => {
  // 加载报警数据的逻辑
  const params: any = {
    pointId: typeModeJyQt.value,
    current: alarmCurrentPage.value,
    size: alarmPageSize.value,
  };

  if (timeArrQt.value) {
    const [startTime, endTime] = timeArrQt.value;
    params.startTime = startTime.format('YYYY-MM-DD HH:mm:ss');
    params.endTime = endTime.format('YYYY-MM-DD HH:mm:ss');
  }

  // 这里应该调用相应的API
  console.log('加载报警数据:', params);
};

// 重置处理
const resetHandle = () => {
  typeModeJyQt.value = '';
  timeArrQt.value = undefined;
  alarmTableData.value = [];
  alarmCurrentPage.value = 1;
  alarmTotal.value = 0;
};

// 分页处理
const handleCurrentChange = (page: number) => {
  alarmCurrentPage.value = page;
  loadAlarmData();
};

const handleSizeChange = (size: number) => {
  alarmPageSize.value = size;
  alarmCurrentPage.value = 1;
  loadAlarmData();
};

// 初始化数据加载
const loadInitialData = async () => {
  try {
    fullscreenLoading.value = true;

    if (mainModel.value === '0') {
      // 加载污染源树形数据
      const treeRes = await getTreeNode({ type: 'pollution' });
      if ((treeRes as any).code === 200) {
        treeData.value = (treeRes as any).data || [];
      }
    } else {
      // 加载甲烷模块树形数据
      const deptRes = await getJoinDept();
      if ((deptRes as any).code === 200) {
        treeDataJy.value = (deptRes as any).data || [];
      }
    }
  } catch (error) {
    console.error('初始化数据加载失败:', error);
    message.error('初始化数据加载失败');
  } finally {
    fullscreenLoading.value = false;
  }
};

// 监听器
watch(
  () => mainModel.value,
  () => {
    loadInitialData();
  }
);

watch(
  () => filterText.value,
  newValue => {
    // 过滤树形数据
    if (monitorListRef.value) {
      monitorListRef.value.filter(newValue);
    }
  }
);

// 生命周期
onMounted(() => {
  loadInitialData();
  // 设置默认日期
  environmentalData.day = dayjs();
});

onBeforeUnmount(() => {
  // 清理工作
});
</script>

<style lang="less" scoped>
::v-deep .ant-spin-nested-loading {
  width: 100%;
  height: 100%;
  .ant-spin-container {
    width: 100%;
    height: 100%;
  }
}
.environmental-overview-page {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.screen-map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

// .main-background {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background: rgba(0, 0, 0, 0.3);
//   z-index: 2;
// }

.main-tree {
  position: absolute;
  left: 0px;
  width: 390px;
  height: 100%;
  padding: 24px;
  z-index: 10;
  transition: all 0.3s ease;

  &.main-tree-s {
    width: 60px;
    padding: 16px 8px;

    .search {
      display: none;
    }

    .tree-wrapper {
      height: calc(100% - 52px);
      overflow: auto;
      margin-top: 20px;
    }
  }

  .search {
    margin-bottom: 16px;
  }

  .tree-wrapper {
    height: calc(100% - 52px);
    overflow: auto;
    margin-top: 20px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(83, 240, 233, 0.6);
      border-radius: 2px;
    }
  }
}

// 左侧按钮
// 左侧按钮
.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  top: calc((100% - 89px) / 2);
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  opacity: 0.8;
  z-index: 9;

  &.left-img-s {
    left: 0;
  }
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  top: calc((100% - 50px) / 2);
  transform: rotate(180deg);
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  opacity: 0.8;
  z-index: 99;

  &:hover {
    opacity: 1;
  }

  &.left-img-icon-s {
    left: -8px;
    transform: rotate(0deg);
  }
}

.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

// .draggable-menu.active {
//   transform: scale(1.02);
// }

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.control {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  transition: all 0.3s ease;

  &.control-s {
    right: 20px;
  }

  .control-div {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .img {
      width: 40px;
      height: 40px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(83, 240, 233, 0.3);
      }

      &.img1 {
        position: relative;
      }
    }

    .imgClick {
      position: absolute;
      top: 48px;
      right: 0;
      display: flex;
      flex-direction: column;

      div {
        width: 40px;
        height: 20px;
        cursor: pointer;
        background: rgba(83, 240, 233, 0.2);
        border: 1px solid rgba(83, 240, 233, 0.4);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(83, 240, 233, 0.4);
        }

        &:first-child {
          border-radius: 4px 4px 0 0;
        }

        &:last-child {
          border-radius: 0 0 4px 4px;
          border-top: none;
        }
      }
    }
  }
}

.select {
  position: absolute;
  top: 22px;
  left: 414px;
  width: 200px;
  z-index: 10;
  transition: all 0.3s ease;

  &.select-s {
    left: 24px;
  }
}

.card {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 10;
  transition: all 0.3s ease;

  &.card-jy {
    left: 340px;
  }

  .card-top {
    margin-bottom: 16px;
  }
}

.box-card {
  margin-bottom: 16px;
}

.select-jy {
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(11, 32, 87, 0.6);
  border: 1px solid rgba(83, 240, 233, 0.3);
  border-radius: 8px;

  &.select-jy-jy {
    background: rgba(11, 32, 87, 0.8);
  }

  :deep(.ant-btn) {
    margin-left: 8px;
  }
}

.content-jy {
  padding: 16px;
  background: rgba(11, 32, 87, 0.6);
  border: 1px solid rgba(83, 240, 233, 0.3);
  border-radius: 8px;
}

.custom-tree-node {
  color: #fff;
  font-size: 14px;
}

.preview-image {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90vw;
  max-height: 90vh;
  z-index: 1000;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
